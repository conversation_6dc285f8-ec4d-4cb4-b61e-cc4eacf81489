import React, { useState, useEffect } from 'react';
import {
  ScrollView,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { supabase } from '../lib/supabase';
import SubmitCard, { SubmitCardType } from '../components/SubmitCard';

export default function ResetPassword() {
  const router = useRouter();
  const { access_token, refresh_token } = useLocalSearchParams();
  
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Submit card state
  const [submitCardVisible, setSubmitCardVisible] = useState(false);
  const [submitCardType, setSubmitCardType] = useState<SubmitCardType>('success');
  const [submitCardTitle, setSubmitCardTitle] = useState('');
  const [submitCardMessage, setSubmitCardMessage] = useState('');

  // Password validation
  const isPasswordValid = newPassword.length >= 8;
  const doPasswordsMatch = newPassword === confirmPassword && confirmPassword.length > 0;
  const isFormValid = isPasswordValid && doPasswordsMatch;

  const showSubmitCard = (
    type: SubmitCardType,
    title: string,
    message: string
  ) => {
    setSubmitCardType(type);
    setSubmitCardTitle(title);
    setSubmitCardMessage(message);
    setSubmitCardVisible(true);
  };

  useEffect(() => {
    // Set the session if tokens are provided
    if (access_token && refresh_token) {
      supabase.auth.setSession({
        access_token: access_token as string,
        refresh_token: refresh_token as string,
      });
    }
  }, [access_token, refresh_token]);

  const handleResetPassword = async () => {
    if (!isFormValid) {
      if (!isPasswordValid) {
        showSubmitCard(
          'error',
          'Password Too Short',
          'Password must be at least 8 characters long.'
        );
      } else if (!doPasswordsMatch) {
        showSubmitCard(
          'error',
          'Passwords Don\'t Match',
          'Please make sure both passwords are identical.'
        );
      }
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        showSubmitCard(
          'error',
          'Reset Failed',
          error.message || 'Failed to reset password. Please try again.'
        );
      } else {
        showSubmitCard(
          'success',
          'Password Updated',
          'Your password has been successfully updated. You can now sign in with your new password.'
        );
        
        // Redirect to login after a delay
        setTimeout(() => {
          router.replace('/login');
        }, 3000);
      }
    } catch (error) {
      showSubmitCard(
        'error',
        'Something Went Wrong',
        'An unexpected error occurred. Please try again later.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <ScrollView className="flex-1 bg-zinc-900" showsVerticalScrollIndicator={false}>
        <View className="p-6 pt-16">
          {/* Header */}
          <View className="flex-row items-center justify-between mb-8">
            <TouchableOpacity
              onPress={() => router.back()}
              className="w-10 h-10 bg-zinc-800/50 rounded-xl items-center justify-center"
              activeOpacity={0.7}
            >
              <Ionicons name="arrow-back" size={20} color="#d4d4d8" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-white">Reset Password</Text>
            <View className="w-10" />
          </View>

          {/* Supportive Message */}
          <View className="bg-gradient-to-r from-green-500/15 via-blue-500/10 to-green-500/15 border border-green-400/30 rounded-3xl p-5 mb-8">
            <View className="flex-row items-center mb-3">
              <View className="w-8 h-8 bg-green-500/20 rounded-full items-center justify-center mr-3">
                <Ionicons name="shield-checkmark" size={16} color="#22c55e" />
              </View>
              <Text className="text-green-300 font-semibold text-base">Almost There!</Text>
            </View>
            <Text className="text-zinc-200 text-sm leading-relaxed">
              Create a new, secure password for your account. Make sure it's something you'll remember but others can't guess.
            </Text>
          </View>

          {/* New Password Field */}
          <View className="mb-4">
            <Text className="text-white font-medium mb-3 text-base">New Password</Text>
            <View
              className={`bg-zinc-800/80 rounded-2xl border-2 py-3 px-4 flex-row items-center ${
                focusedField === "newPassword" ? "border-green-400/50" : "border-zinc-700/50"
              }`}
            >
              <View className="w-10 h-10 bg-zinc-700/50 rounded-xl items-center justify-center mr-3">
                <Ionicons name="lock-closed-outline" size={20} color="#a1a1aa" />
              </View>
              <TextInput
                value={newPassword}
                onChangeText={setNewPassword}
                onFocus={() => setFocusedField("newPassword")}
                onBlur={() => setFocusedField(null)}
                placeholder="Enter your new password"
                placeholderTextColor="#6b7280"
                secureTextEntry={!showNewPassword}
                className="text-white flex-1 text-base"
                editable={!loading}
              />
              <TouchableOpacity
                onPress={() => setShowNewPassword(!showNewPassword)}
                className="w-10 h-10 items-center justify-center"
                activeOpacity={0.7}
              >
                <Ionicons name={showNewPassword ? "eye-off-outline" : "eye-outline"} size={20} color="#a1a1aa" />
              </TouchableOpacity>
            </View>
            {newPassword.length > 0 && (
              <Text className={`text-sm mt-2 ${isPasswordValid ? 'text-green-400' : 'text-red-400'}`}>
                {isPasswordValid ? '✓ Password meets requirements' : '✗ Password must be at least 8 characters'}
              </Text>
            )}
          </View>

          {/* Confirm Password Field */}
          <View className="mb-8">
            <Text className="text-white font-medium mb-3 text-base">Confirm Password</Text>
            <View
              className={`bg-zinc-800/80 rounded-2xl border-2 py-3 px-4 flex-row items-center ${
                focusedField === "confirmPassword" ? "border-green-400/50" : "border-zinc-700/50"
              }`}
            >
              <View className="w-10 h-10 bg-zinc-700/50 rounded-xl items-center justify-center mr-3">
                <Ionicons name="lock-closed-outline" size={20} color="#a1a1aa" />
              </View>
              <TextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                onFocus={() => setFocusedField("confirmPassword")}
                onBlur={() => setFocusedField(null)}
                placeholder="Confirm your new password"
                placeholderTextColor="#6b7280"
                secureTextEntry={!showConfirmPassword}
                className="text-white flex-1 text-base"
                editable={!loading}
              />
              <TouchableOpacity
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                className="w-10 h-10 items-center justify-center"
                activeOpacity={0.7}
              >
                <Ionicons name={showConfirmPassword ? "eye-off-outline" : "eye-outline"} size={20} color="#a1a1aa" />
              </TouchableOpacity>
            </View>
            {confirmPassword.length > 0 && (
              <Text className={`text-sm mt-2 ${doPasswordsMatch ? 'text-green-400' : 'text-red-400'}`}>
                {doPasswordsMatch ? '✓ Passwords match' : '✗ Passwords do not match'}
              </Text>
            )}
          </View>

          {/* Update Password Button */}
          <TouchableOpacity
            className={`rounded-2xl py-5 mb-6 ${
              isFormValid && !loading ? "bg-green-500" : "bg-zinc-700/50"
            }`}
            disabled={!isFormValid || loading}
            activeOpacity={0.8}
            onPress={handleResetPassword}
          >
            <View className="flex-row items-center justify-center">
              {loading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <>
                  <Text
                    className={`font-bold text-lg mr-2 ${
                      isFormValid ? "text-white" : "text-zinc-400"
                    }`}
                  >
                    Update Password
                  </Text>
                  <Ionicons
                    name="checkmark-circle"
                    size={20}
                    color={isFormValid ? "white" : "#6b7280"}
                  />
                </>
              )}
            </View>
          </TouchableOpacity>

          {/* Security Tips */}
          <View className="bg-zinc-800/40 rounded-2xl p-5 border border-zinc-700/30">
            <View className="flex-row items-center mb-3">
              <View className="w-8 h-8 bg-blue-500/20 rounded-full items-center justify-center mr-3">
                <Ionicons name="information-circle" size={16} color="#3b82f6" />
              </View>
              <Text className="text-blue-300 font-semibold">Security Tips</Text>
            </View>
            <Text className="text-zinc-300 text-sm leading-relaxed">
              • Use a mix of letters, numbers, and symbols{'\n'}
              • Avoid using personal information{'\n'}
              • Don't reuse passwords from other accounts{'\n'}
              • Consider using a password manager
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Submit Card for feedback */}
      <SubmitCard
        visible={submitCardVisible}
        type={submitCardType}
        title={submitCardTitle}
        message={submitCardMessage}
        onClose={() => setSubmitCardVisible(false)}
        autoClose={submitCardType === 'success'}
        autoCloseDelay={4000}
      />
    </>
  );
}
