import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../lib/supabase';
import SubmitCard, { SubmitCardType } from './SubmitCard';

interface ForgotPasswordModalProps {
  visible: boolean;
  onClose: () => void;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  visible,
  onClose,
}) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  
  // Submit card state
  const [submitCardVisible, setSubmitCardVisible] = useState(false);
  const [submitCardType, setSubmitCardType] = useState<SubmitCardType>('success');
  const [submitCardTitle, setSubmitCardTitle] = useState('');
  const [submitCardMessage, setSubmitCardMessage] = useState('');

  const isEmailValid = email.length > 0 && email.includes('@');

  const showSubmitCard = (
    type: SubmitCardType,
    title: string,
    message: string
  ) => {
    setSubmitCardType(type);
    setSubmitCardTitle(title);
    setSubmitCardMessage(message);
    setSubmitCardVisible(true);
  };

  const handleResetPassword = async () => {
    if (!isEmailValid) {
      showSubmitCard(
        'error',
        'Invalid Email',
        'Please enter a valid email address.'
      );
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'aitherapist://reset-password',
      });

      if (error) {
        showSubmitCard(
          'error',
          'Reset Failed',
          error.message || 'Failed to send reset email. Please try again.'
        );
      } else {
        showSubmitCard(
          'success',
          'Reset Email Sent',
          `We've sent a password reset link to ${email}. Please check your inbox and follow the instructions.`
        );
        setEmail(''); // Clear the email field on success
      }
    } catch (error) {
      showSubmitCard(
        'error',
        'Something Went Wrong',
        'An unexpected error occurred. Please try again later.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setFocusedField(null);
    onClose();
  };

  return (
    <>
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleClose}
      >
        <ScrollView className="flex-1 bg-zinc-900" showsVerticalScrollIndicator={false}>
          <View className="p-6 pt-16">
            {/* Header */}
            <View className="flex-row items-center justify-between mb-8">
              <TouchableOpacity
                onPress={handleClose}
                className="w-10 h-10 bg-zinc-800/50 rounded-xl items-center justify-center"
                activeOpacity={0.7}
              >
                <Ionicons name="close" size={20} color="#d4d4d8" />
              </TouchableOpacity>
              <Text className="text-xl font-bold text-white">Reset Password</Text>
              <View className="w-10" />
            </View>

            {/* Supportive Message */}
            <View className="bg-gradient-to-r from-blue-500/15 via-purple-500/10 to-blue-500/15 border border-blue-400/30 rounded-3xl p-5 mb-8">
              <View className="flex-row items-center mb-3">
                <View className="w-8 h-8 bg-blue-500/20 rounded-full items-center justify-center mr-3">
                  <Ionicons name="key" size={16} color="#60a5fa" />
                </View>
                <Text className="text-blue-300 font-semibold text-base">We're Here to Help</Text>
              </View>
              <Text className="text-zinc-200 text-sm leading-relaxed">
                Forgot your password? No worries! Enter your email address and we'll send you a secure link to reset it.
              </Text>
            </View>

            {/* Email Input */}
            <View className="mb-8">
              <Text className="text-white font-medium mb-3 text-base">Email Address</Text>
              <View
                className={`bg-zinc-800/80 rounded-2xl border-2 py-3 px-4 flex-row items-center ${
                  focusedField === "email" ? "border-green-400/50" : "border-zinc-700/50"
                }`}
              >
                <View className="w-10 h-10 bg-zinc-700/50 rounded-xl items-center justify-center mr-3">
                  <Ionicons name="mail-outline" size={20} color="#a1a1aa" />
                </View>
                <TextInput
                  value={email}
                  onChangeText={setEmail}
                  onFocus={() => setFocusedField("email")}
                  onBlur={() => setFocusedField(null)}
                  placeholder="Enter your email address"
                  placeholderTextColor="#6b7280"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  className="text-white flex-1 text-base"
                  editable={!loading}
                />
                {isEmailValid && <Ionicons name="checkmark-circle" size={20} color="#22c55e" />}
              </View>
            </View>

            {/* Reset Button */}
            <TouchableOpacity
              className={`rounded-2xl py-5 mb-6 ${
                isEmailValid && !loading ? "bg-green-500" : "bg-zinc-700/50"
              }`}
              disabled={!isEmailValid || loading}
              activeOpacity={0.8}
              onPress={handleResetPassword}
            >
              <View className="flex-row items-center justify-center">
                {loading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Text
                      className={`font-bold text-lg mr-2 ${
                        isEmailValid ? "text-white" : "text-zinc-400"
                      }`}
                    >
                      Send Reset Link
                    </Text>
                    <Ionicons
                      name="paper-plane"
                      size={20}
                      color={isEmailValid ? "white" : "#6b7280"}
                    />
                  </>
                )}
              </View>
            </TouchableOpacity>

            {/* Help Section */}
            <View className="bg-zinc-800/40 rounded-2xl p-5 border border-zinc-700/30">
              <View className="flex-row items-center mb-3">
                <View className="w-8 h-8 bg-amber-500/20 rounded-full items-center justify-center mr-3">
                  <Ionicons name="help-circle" size={16} color="#f59e0b" />
                </View>
                <Text className="text-amber-300 font-semibold">Need More Help?</Text>
              </View>
              <Text className="text-zinc-300 text-sm leading-relaxed mb-3">
                If you don't receive the reset email within a few minutes, check your spam folder or contact our support team.
              </Text>
              <TouchableOpacity
                className="flex-row items-center"
                activeOpacity={0.7}
              >
                <Text className="text-amber-400 font-semibold mr-2">Contact Support</Text>
                <Ionicons name="arrow-forward" size={16} color="#f59e0b" />
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </Modal>

      {/* Submit Card for feedback */}
      <SubmitCard
        visible={submitCardVisible}
        type={submitCardType}
        title={submitCardTitle}
        message={submitCardMessage}
        onClose={() => setSubmitCardVisible(false)}
        autoClose={submitCardType === 'success'}
        autoCloseDelay={4000}
      />
    </>
  );
};

export default ForgotPasswordModal;
